const express = require('express');

const app = express();
const morgan = require('morgan');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { accessibleRecordsPlugin } = require('@casl/mongoose');
const cors = require('cors');
const passport = require('passport');
require('./api/middleware/passport');
const path = require("path");

// Import security middleware
const {
  securityHeaders,
  rateLimitConfigs,
  adminIpRestriction,
  sanitizeRequest,
  securityMonitoring
} = require('./api/middleware/securityConfig');

const { securityValidationMiddleware } = require('./api/middleware/securityLogger');

// Import error handling middleware
const { handleError, notFound } = require('./api/middleware/errorHandler');

// Import image handling middleware
const { handleMissingImage } = require('./api/middleware/imageHandler');

// Import analytics middleware
const { trackPageView } = require('./api/middleware/analytics');

mongoose.plugin(accessibleRecordsPlugin);
mongoose.set('strictQuery', true);

dotenv.config();

mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true
});
mongoose.Promise = global.Promise;

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(sanitizeRequest);
app.use(securityMonitoring);
app.use(securityValidationMiddleware);
app.use(rateLimitConfigs.general);

// CORS configuration - restrictive
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'https://blog.aithietke.com',
      'https://cdn1.aithietke.com',
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8000'
    ];
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-Access-Token'
  ]
};

app.use(cors(corsOptions));

// Analytics tracking middleware (before static files and routes)
app.use(trackPageView);

// Handle missing images before serving static files
app.use('/uploads', handleMissingImage);

// Static files with CORS headers
app.use('/uploads', (req, res, next) => {
  const origin = req.headers.origin;
  const allowedOrigins = [
    'https://blog.aithietke.com',
    'https://cdn1.aithietke.com',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:8000'
  ];
  
  // Set CORS headers for static files
  if (origin && allowedOrigins.indexOf(origin) !== -1) {
    // For requests with allowed origins
    res.header('Access-Control-Allow-Origin', origin);
    res.header('Access-Control-Allow-Credentials', 'true');
  } else if (!origin) {
    // For direct access (no origin), don't set credentials
    res.header('Access-Control-Allow-Origin', '*');
  }
  
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
  
  next();
}, express.static(path.join(__dirname, "uploads")));

// App Use Library
app.use(morgan('dev'));
// Log thay đổi của app Get/ Post ...
// parse application/x-www-form-urlencoded

app.use(express.json());
app.use(express.urlencoded({
  extended: true,
}));
app.use(passport.initialize());

// Remove manual CORS headers as we're using proper CORS middleware now


// Import router
const userRouter = require('./api/routes/user');
const adminRouter = require('./api/routes/admin');
const settingRouter = require('./api/routes/setting');
const mediaRouter = require('./api/routes/media');
const postCatRouter = require('./api/routes/post_cat');
const postRouter = require('./api/routes/post');
const menuRouter = require('./api/routes/menu');
const videoRouter = require('./api/routes/video');
const pageRouter = require('./api/routes/page');


// Export router
app.use('/api/auth', rateLimitConfigs.auth, userRouter);
app.use('/api/administrator', rateLimitConfigs.admin, adminIpRestriction, adminRouter);
app.use('/api/setting', rateLimitConfigs.general, settingRouter);
app.use('/api/media', rateLimitConfigs.upload, mediaRouter);
app.use('/api/post_cat', rateLimitConfigs.general, postCatRouter);
app.use('/api/post', rateLimitConfigs.general, postRouter);
app.use('/api/menu', rateLimitConfigs.general, menuRouter);
app.use('/api/video', rateLimitConfigs.upload, videoRouter);
app.use('/api/page', rateLimitConfigs.general, pageRouter);


app.get('/', (req, res) => {
  res.send('Welcome VPS!');
});

// Error handling middleware
app.use(notFound);
app.use(handleError);
  
module.exports = app;
