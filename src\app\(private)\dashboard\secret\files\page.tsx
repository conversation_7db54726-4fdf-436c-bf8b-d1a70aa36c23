"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import fileApiRequest from "@/apiRequests/file";
import { FileItemType, FileSearchType } from "@/schemaValidations/file.schema";
import FileList from "@/components/FileManager/FileList";
import FileUploadModal from "@/components/FileManager/FileUploadModal";
import { formatBytes, formatNumber } from "@/utils/formatters";

const FileManagementPage = () => {
  const [files, setFiles] = useState<FileItemType[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [searchParams, setSearchParams] = useState<FileSearchType>({
    page: 1,
    perPage: 20,
    type: 'all',
    sortBy: 'uploadedAt',
    sortOrder: 'desc'
  });
  const [totalFiles, setTotalFiles] = useState(0);
  const [selectedFile, setSelectedFile] = useState<FileItemType | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);

  // Fetch files
  const fetchFiles = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await fileApiRequest.getFiles(searchParams, sessionToken);
      
      if (result.payload.success) {
        setFiles(result.payload.files);
        setTotalFiles(result.payload.total || 0);
      } else {
        toast.error("Failed to fetch files");
      }
    } catch (error) {
      console.error("Error fetching files:", error);
      toast.error("An error occurred while fetching files");
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStats = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await fileApiRequest.getFileStats(sessionToken);
      
      if (result.payload.success) {
        setStats(result.payload.stats);
      }
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  // Delete file
  const handleFileDelete = async (fileId: string) => {
    if (!confirm("Are you sure you want to delete this file?")) return;

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await fileApiRequest.deleteFile(fileId, sessionToken);
      
      if (result.payload.success) {
        toast.success("File deleted successfully");
        fetchFiles();
        fetchStats();
      } else {
        toast.error("Failed to delete file");
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      toast.error("An error occurred while deleting file");
    }
  };

  // Bulk actions
  const handleBulkAction = async (fileIds: string[], action: string) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await fileApiRequest.bulkAction({ fileIds, action }, sessionToken);
      
      if (result.payload.success) {
        toast.success(`${action} completed successfully`);
        fetchFiles();
        fetchStats();
      } else {
        toast.error(`Failed to ${action} files`);
      }
    } catch (error) {
      console.error(`Error in bulk ${action}:`, error);
      toast.error(`An error occurred while performing ${action}`);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchParams(prev => ({ ...prev, query, page: 1 }));
  };

  // Handle filter change
  const handleFilterChange = (key: keyof FileSearchType, value: any) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({ ...prev, page }));
  };

  useEffect(() => {
    fetchFiles();
  }, [searchParams]);

  useEffect(() => {
    fetchStats();
  }, []);

  const totalPages = Math.ceil(totalFiles / searchParams.perPage);

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">File Management</h1>
        <p className="text-gray-600">Manage uploaded files and media</p>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">📁</div>
              <div>
                <p className="text-sm text-gray-600">Total Files</p>
                <p className="text-xl font-semibold">{formatNumber(stats.totalFiles)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">💾</div>
              <div>
                <p className="text-sm text-gray-600">Total Size</p>
                <p className="text-xl font-semibold">{formatBytes(stats.totalSize)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">📈</div>
              <div>
                <p className="text-sm text-gray-600">Recent Uploads</p>
                <p className="text-xl font-semibold">{formatNumber(stats.recentUploads)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl mr-3">🎯</div>
              <div>
                <p className="text-sm text-gray-600">File Types</p>
                <p className="text-xl font-semibold">{stats.filesByType.length}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          {/* Search */}
          <div className="flex-1 min-w-64">
            <input
              type="text"
              placeholder="Search files..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>

          {/* Type Filter */}
          <select
            value={searchParams.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="image">Images</option>
            <option value="video">Videos</option>
            <option value="document">Documents</option>
            <option value="other">Other</option>
          </select>

          {/* Sort */}
          <select
            value={`${searchParams.sortBy}-${searchParams.sortOrder}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleFilterChange('sortBy', sortBy);
              handleFilterChange('sortOrder', sortOrder);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="uploadedAt-desc">Newest First</option>
            <option value="uploadedAt-asc">Oldest First</option>
            <option value="filename-asc">Name A-Z</option>
            <option value="filename-desc">Name Z-A</option>
            <option value="size-desc">Largest First</option>
            <option value="size-asc">Smallest First</option>
          </select>

          {/* Upload Button */}
          <button
            onClick={() => setShowUploadModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            📤 Upload File
          </button>
        </div>
      </div>

      {/* File List */}
      <FileList
        files={files}
        onFileSelect={setSelectedFile}
        onFileDelete={handleFileDelete}
        onBulkAction={handleBulkAction}
        loading={loading}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-center">
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(searchParams.page - 1)}
              disabled={searchParams.page <= 1}
              className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-2 border rounded-md ${
                    searchParams.page === page
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              );
            })}
            
            <button
              onClick={() => handlePageChange(searchParams.page + 1)}
              disabled={searchParams.page >= totalPages}
              className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <FileUploadModal
          onClose={() => setShowUploadModal(false)}
          onUploadSuccess={() => {
            fetchFiles();
            fetchStats();
            setShowUploadModal(false);
          }}
        />
      )}

      {/* File Details Modal */}
      {selectedFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">File Details</h3>
              <button
                onClick={() => setSelectedFile(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="space-y-3">
              <div><strong>Name:</strong> {selectedFile.originalName}</div>
              <div><strong>Type:</strong> {selectedFile.type}</div>
              <div><strong>Size:</strong> {formatBytes(selectedFile.size)}</div>
              <div><strong>Uploaded by:</strong> {selectedFile.uploadedBy.username}</div>
              <div><strong>Upload date:</strong> {new Date(selectedFile.uploadedAt).toLocaleString()}</div>
              <div><strong>Status:</strong> {selectedFile.isActive ? 'Active' : 'Inactive'}</div>
              {selectedFile.description && (
                <div><strong>Description:</strong> {selectedFile.description}</div>
              )}
              {selectedFile.tags && selectedFile.tags.length > 0 && (
                <div><strong>Tags:</strong> {selectedFile.tags.join(', ')}</div>
              )}
            </div>

            <div className="mt-6 flex gap-2">
              <button
                onClick={() => window.open(selectedFile.url, '_blank')}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                View/Download
              </button>
              <button
                onClick={() => {
                  handleFileDelete(selectedFile._id);
                  setSelectedFile(null);
                }}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileManagementPage;
