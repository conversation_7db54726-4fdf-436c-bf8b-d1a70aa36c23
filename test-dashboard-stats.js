const http = require('http');

// Test dashboard stats API
const options = {
  hostname: 'localhost',
  port: 8000,
  path: '/api/administrator/dashboard-stats',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer test-token'
  }
};

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Cache-Control: ${res.headers['cache-control']}`);
  console.log(`Pragma: ${res.headers['pragma']}`);
  console.log(`Expires: ${res.headers['expires']}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response body:', data);
    try {
      const parsed = JSON.parse(data);
      if (parsed.success && parsed.stats) {
        console.log('\n=== DASHBOARD STATS ===');
        console.log('Total Posts:', parsed.stats.totalPosts);
        console.log('Total Users:', parsed.stats.totalUsers);
        console.log('Total Pages:', parsed.stats.totalPages);
        console.log('Total Views:', parsed.stats.totalViews);
        console.log('Pending Posts:', parsed.stats.pendingPosts);
        console.log('Active Users:', parsed.stats.activeUsers);
      }
    } catch (e) {
      console.log('Could not parse JSON response');
    }
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.end();
