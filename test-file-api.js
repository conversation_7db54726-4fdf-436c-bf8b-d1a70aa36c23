const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true
});

// Import File model
const File = require('./api/models/file');

async function testFileAPI() {
  try {
    console.log('🔍 Testing File API...');
    
    // Test 1: Check database connection
    console.log('📡 Database connection state:', mongoose.connection.readyState);
    
    // Test 2: Count total files in database
    const totalFiles = await File.countDocuments();
    console.log('📊 Total files in database:', totalFiles);
    
    // Test 3: Count active files
    const activeFiles = await File.countDocuments({ isActive: true });
    console.log('✅ Active files:', activeFiles);
    
    // Test 4: Get first 5 files
    const sampleFiles = await File.find().limit(5).lean();
    console.log('📁 Sample files:');
    sampleFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.originalName} (${file.type}) - Active: ${file.isActive}`);
    });
    
    // Test 5: Test the same query as the API
    const apiQuery = { isActive: true };
    const apiFiles = await File.find(apiQuery)
      .populate('uploadedBy', 'username email')
      .sort({ createdAt: -1 })
      .limit(20)
      .lean();
    
    console.log('🔍 API Query Result:', apiFiles.length, 'files found');
    
    // Test 6: Check file statistics
    const stats = await File.getFileStatistics();
    console.log('📈 File Statistics:', stats);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

testFileAPI();
