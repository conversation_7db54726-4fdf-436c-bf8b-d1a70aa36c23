const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function createPlaceholderImage() {
  try {
    // Create uploads/media directory if it doesn't exist
    const mediaDir = path.join(__dirname, 'uploads', 'media');
    if (!fs.existsSync(mediaDir)) {
      fs.mkdirSync(mediaDir, { recursive: true });
    }

    // Create a simple placeholder image
    const placeholderPath = path.join(mediaDir, 'placeholder.jpg');
    
    // Create a 400x300 gray placeholder with text
    const svg = `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#f3f4f6"/>
        <text x="200" y="140" font-family="Arial, sans-serif" font-size="16" fill="#6b7280" text-anchor="middle">
          <PERSON><PERSON><PERSON> <PERSON><PERSON> không tồn tại
        </text>
        <text x="200" y="170" font-family="Arial, sans-serif" font-size="14" fill="#9ca3af" text-anchor="middle">
          Image not found
        </text>
      </svg>
    `;

    await sharp(Buffer.from(svg))
      .jpeg({ quality: 80 })
      .toFile(placeholderPath);

    console.log('Placeholder image created at:', placeholderPath);

    // Create thumbnail version
    const thumbPath = path.join(mediaDir, 'thumb_placeholder.jpg');
    await sharp(Buffer.from(svg))
      .resize(200, 150)
      .jpeg({ quality: 80 })
      .toFile(thumbPath);

    console.log('Placeholder thumbnail created at:', thumbPath);

  } catch (error) {
    console.error('Error creating placeholder image:', error);
  }
}

createPlaceholderImage();
