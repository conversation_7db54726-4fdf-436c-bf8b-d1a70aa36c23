const fs = require('fs');
const path = require('path');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
const dependencies = Object.keys(packageJson.dependencies);

console.log('Total dependencies in package.json:', dependencies.length);
console.log('Dependencies:', dependencies);

// Function to recursively find all JavaScript files
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and hidden directories
      if (!file.startsWith('.') && file !== 'node_modules') {
        findJsFiles(filePath, fileList);
      }
    } else if (file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Find all JavaScript files
const jsFiles = findJsFiles('./');
console.log('\nFound', jsFiles.length, 'JavaScript files');

// Function to extract require statements from a file
function extractRequires(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const requireMatches = content.match(/require\s*\(\s*['"](.*?)['"]\s*\)/g);
    
    if (!requireMatches) return [];
    
    return requireMatches
      .map(match => {
        const moduleMatch = match.match(/require\s*\(\s*['"](.*?)['"]\s*\)/);
        return moduleMatch ? moduleMatch[1] : null;
      })
      .filter(module => module && !module.startsWith('.') && !module.startsWith('/')) // Filter out relative imports
      .map(module => {
        // Extract main package name (remove sub-paths)
        if (module.startsWith('@')) {
          // Handle scoped packages like @casl/ability
          const parts = module.split('/');
          return parts.slice(0, 2).join('/');
        } else {
          // Handle regular packages
          return module.split('/')[0];
        }
      });
  } catch (error) {
    console.error('Error reading file', filePath, ':', error.message);
    return [];
  }
}

// Extract all used packages
const usedPackages = new Set();

jsFiles.forEach(filePath => {
  const requires = extractRequires(filePath);
  requires.forEach(pkg => usedPackages.add(pkg));
});

// Filter to only include packages that are in our dependencies
const usedDependencies = Array.from(usedPackages).filter(pkg => 
  dependencies.includes(pkg)
);

console.log('\nUsed dependencies from package.json:', usedDependencies.length);
console.log('Used dependencies:', usedDependencies.sort());

// Find unused dependencies
const unusedDependencies = dependencies.filter(dep => 
  !usedDependencies.includes(dep)
);

console.log('\nUnused dependencies:', unusedDependencies.length);
console.log('Unused dependencies:', unusedDependencies.sort());

// Also check for commonly used packages that might be indirectly used
const builtInModules = ['fs', 'path', 'http', 'https', 'crypto', 'url', 'os', 'util', 'stream', 'events', 'buffer', 'zlib'];
const allUsedPackages = Array.from(usedPackages).filter(pkg => !builtInModules.includes(pkg));

console.log('\nAll external packages used (including transitive):', allUsedPackages.sort());

// Create a report
const report = {
  totalDependencies: dependencies.length,
  usedDependencies: usedDependencies.sort(),
  unusedDependencies: unusedDependencies.sort(),
  usageRate: Math.round((usedDependencies.length / dependencies.length) * 100) + '%'
};

fs.writeFileSync('./dependency-analysis.json', JSON.stringify(report, null, 2));
console.log('\nAnalysis saved to dependency-analysis.json');
console.log(`Usage rate: ${report.usageRate} (${usedDependencies.length}/${dependencies.length})`);
