const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { cleanupMissingImages } = require('./api/middleware/imageHandler');
const Post = require('./api/models/post');

dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function runCleanup() {
  try {
    console.log('Starting cleanup of missing images...');
    
    // Clean up missing images in posts
    const updatedCount = await cleanupMissingImages(Post);
    
    console.log(`Cleanup completed. Updated ${updatedCount} posts.`);
    
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed.');
    
  } catch (error) {
    console.error('Error during cleanup:', error);
    process.exit(1);
  }
}

// Run the cleanup
runCleanup();
