{"name": "blog-express", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^5.1.1", "@minoru/react-dnd-treeview": "^3.5.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@react-oauth/google": "^0.12.2", "@tanstack/react-query": "^5.80.7", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-bubble-menu": "^2.14.0", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-font-family": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/lodash": "^4.17.17", "algoliasearch": "^5.28.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^4.3.0", "crypto-js": "^4.2.0", "daisyui": "^4.12.10", "dotenv": "^16.5.0", "flowbite": "^2.4.1", "flowbite-react": "^0.10.1", "idb": "^8.0.0", "instantsearch.css": "^8.3.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.516.0", "nanoid": "^5.1.5", "next": "^15.3.3", "next-share": "^0.27.0", "next-sitemap": "^4.2.3", "nextjs-toploader": "^1.6.12", "papaparse": "^5.5.3", "pdfjs-dist": "^5.3.31", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-dnd": "^16.0.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.58.1", "react-instantsearch": "^7.12.2", "react-pdf": "^9.2.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "sass": "^1.89.2", "screenfull": "^6.0.2", "sharp": "^0.34.2", "slick-carousel": "^1.8.1", "swr": "^2.3.3", "tippy.js": "^6.3.7", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "^15.3.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}