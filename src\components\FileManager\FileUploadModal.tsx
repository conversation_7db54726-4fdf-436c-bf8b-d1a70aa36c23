"use client";

import { useState, useRef, useCallback } from "react";
import { toast } from "react-toastify";
import fileApiRequest from "@/apiRequests/file";
import { Upload, X, File, Image, Video, FileText } from "react-feather";

interface FileUploadModalProps {
  onClose: () => void;
  onUploadSuccess: () => void;
}

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const FileUploadModal: React.FC<FileUploadModalProps> = ({ onClose, onUploadSuccess }) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Allowed file types and max size (matching backend)
  const allowedTypes = [
    // Images
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    // Videos
    'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm',
    // Documents
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain', 'text/csv'
  ];
  const maxSize = 100 * 1024 * 1024; // 100MB (matching backend)

  // Get file icon based on type
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="w-6 h-6 text-blue-500" />;
    if (file.type.startsWith('video/')) return <Video className="w-6 h-6 text-purple-500" />;
    if (file.type.includes('pdf') || file.type.includes('document') || file.type.includes('text')) 
      return <FileText className="w-6 h-6 text-red-500" />;
    return <File className="w-6 h-6 text-gray-500" />;
  };

  // Format file size
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Validate file
  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return `File type ${file.type} is not allowed`;
    }
    if (file.size > maxSize) {
      return `File size must be less than ${formatBytes(maxSize)}`;
    }
    return null;
  };

  // Add files to upload list
  const addFiles = useCallback((newFiles: FileList | File[]) => {
    const fileArray = Array.from(newFiles);
    const validFiles: UploadFile[] = [];

    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        toast.error(`${file.name}: ${error}`);
        return;
      }

      // Check if file already exists
      const exists = files.some(f => f.file.name === file.name && f.file.size === file.size);
      if (exists) {
        toast.warning(`File ${file.name} is already in the upload list`);
        return;
      }

      validFiles.push({
        file,
        id: Math.random().toString(36).substring(2),
        progress: 0,
        status: 'pending'
      });
    });

    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles]);
    }
  }, [files]);

  // Handle drag events
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles);
    }
  }, [addFiles]);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      addFiles(e.target.files);
    }
  };

  // Remove file from list
  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  // Retry upload for failed file
  const retryUpload = async (id: string) => {
    const file = files.find(f => f.id === id);
    if (file && file.status === 'error') {
      await uploadFile(file);
    }
  };

  // Upload single file
  const uploadFile = async (uploadFile: UploadFile): Promise<boolean> => {
    try {
      const formData = new FormData();
      formData.append('file', uploadFile.file);
      if (description) formData.append('description', description);
      if (tags) formData.append('tags', tags);

      // Update status to uploading
      setFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? { ...f, status: 'uploading' as const, progress: 10 } : f
      ));

      const sessionToken = localStorage.getItem("sessionToken") || "";

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f =>
          f.id === uploadFile.id && f.progress < 90
            ? { ...f, progress: f.progress + 10 }
            : f
        ));
      }, 200);

      const result = await fileApiRequest.uploadFile(formData, sessionToken);

      clearInterval(progressInterval);

      if (result.payload.success) {
        // Update status to success
        setFiles(prev => prev.map(f =>
          f.id === uploadFile.id ? { ...f, status: 'success' as const, progress: 100 } : f
        ));
        return true;
      } else {
        throw new Error(result.payload.message || 'Upload failed');
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Upload failed';

      // Update status to error
      setFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? {
          ...f,
          status: 'error' as const,
          progress: 0,
          error: errorMessage
        } : f
      ));
      return false;
    }
  };

  // Upload all files
  const handleUpload = async () => {
    if (files.length === 0) {
      toast.warning("Please select files to upload");
      return;
    }

    setIsUploading(true);
    let successCount = 0;
    let errorCount = 0;

    // Upload files sequentially to avoid overwhelming the server
    for (const file of files) {
      if (file.status === 'pending') {
        const success = await uploadFile(file);
        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
      }
    }

    setIsUploading(false);

    // Show results
    if (successCount > 0) {
      toast.success(`Successfully uploaded ${successCount} file(s)`);
    }
    if (errorCount > 0) {
      toast.error(`Failed to upload ${errorCount} file(s)`);
    }

    // If all files uploaded successfully, close modal
    if (errorCount === 0 && successCount > 0) {
      setTimeout(() => {
        onUploadSuccess();
      }, 1000);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Upload Files</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isUploading}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Drop Zone */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragOver 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Drag and drop files here, or click to select
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Supported: Images (JPEG, PNG, GIF, WebP), Videos (MP4, AVI, MOV, WMV, WebM), Documents (PDF, Word, Excel, Text, CSV) - Max: {formatBytes(maxSize)}
            </p>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              disabled={isUploading}
            >
              Select Files
            </button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileInputChange}
              accept={allowedTypes.join(',')}
            />
          </div>

          {/* File Metadata */}
          {files.length > 0 && (
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description (optional)
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Enter file description..."
                  disabled={isUploading}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags (optional)
                </label>
                <input
                  type="text"
                  value={tags}
                  onChange={(e) => setTags(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter tags separated by commas..."
                  disabled={isUploading}
                />
              </div>
            </div>
          )}

          {/* File List */}
          {files.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-4">Files to Upload ({files.length})</h3>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {files.map((uploadFile) => (
                  <div key={uploadFile.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    {getFileIcon(uploadFile.file)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {uploadFile.file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatBytes(uploadFile.file.size)}
                      </p>
                      {uploadFile.status === 'uploading' && (
                        <div className="mt-1">
                          <div className="w-full bg-gray-200 rounded-full h-1">
                            <div 
                              className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                              style={{ width: `${uploadFile.progress}%` }}
                            />
                          </div>
                        </div>
                      )}
                      {uploadFile.error && (
                        <p className="text-xs text-red-500 mt-1">{uploadFile.error}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {uploadFile.status === 'success' && (
                        <span className="text-green-500 text-sm font-semibold">✓</span>
                      )}
                      {uploadFile.status === 'error' && (
                        <>
                          <button
                            onClick={() => retryUpload(uploadFile.id)}
                            className="text-blue-500 hover:text-blue-700 text-xs px-2 py-1 border border-blue-300 rounded"
                            disabled={isUploading}
                          >
                            Retry
                          </button>
                          <span className="text-red-500 text-sm">✗</span>
                        </>
                      )}
                      {uploadFile.status === 'uploading' && (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      )}
                      {(uploadFile.status === 'pending' || uploadFile.status === 'error') && (
                        <button
                          onClick={() => removeFile(uploadFile.id)}
                          className="text-gray-400 hover:text-red-500"
                          disabled={isUploading}
                        >
                          <X className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            {files.length > 0 && (
              <>
                {files.filter(f => f.status === 'success').length} of {files.length} uploaded
                {files.filter(f => f.status === 'error').length > 0 && (
                  <span className="text-red-500 ml-2">
                    ({files.filter(f => f.status === 'error').length} failed)
                  </span>
                )}
              </>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={isUploading}
            >
              {files.filter(f => f.status === 'success').length > 0 ? 'Close' : 'Cancel'}
            </button>
            <button
              onClick={handleUpload}
              disabled={files.filter(f => f.status === 'pending' || f.status === 'error').length === 0 || isUploading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUploading
                ? 'Uploading...'
                : `Upload ${files.filter(f => f.status === 'pending' || f.status === 'error').length} File(s)`
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FileUploadModal;
