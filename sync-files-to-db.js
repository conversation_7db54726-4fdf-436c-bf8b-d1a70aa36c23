const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true
});

// Import models
const File = require('./api/models/file');
const User = require('./api/models/user');

async function syncFilesToDatabase() {
  try {
    console.log('🔄 Syncing files from uploads directory to database...');
    
    // Get admin user (assuming first user is admin)
    const adminUser = await User.findOne().sort({ createdAt: 1 });
    if (!adminUser) {
      console.log('❌ No admin user found. Please create a user first.');
      return;
    }
    console.log('👤 Using admin user:', adminUser.username);
    
    const uploadsDir = path.join(__dirname, 'uploads');
    const subdirs = ['images', 'file', 'video', 'documents'];
    
    let totalSynced = 0;
    
    for (const subdir of subdirs) {
      const dirPath = path.join(uploadsDir, subdir);
      
      if (!fs.existsSync(dirPath)) {
        console.log(`📁 Directory ${subdir} does not exist, skipping...`);
        continue;
      }
      
      const files = fs.readdirSync(dirPath);
      console.log(`📁 Processing ${subdir} directory: ${files.length} files`);
      
      for (const filename of files) {
        const filePath = path.join(dirPath, filename);
        const stats = fs.statSync(filePath);
        
        if (!stats.isFile()) continue;
        
        // Check if file already exists in database
        const existingFile = await File.findOne({ filename });
        if (existingFile) {
          console.log(`⏭️  File ${filename} already exists in database, skipping...`);
          continue;
        }
        
        // Extract original name (remove timestamp prefix)
        const originalName = filename.replace(/^\d+-\d+-/, '').replace(/^\d+-/, '');
        
        // Determine mimetype based on extension
        const ext = path.extname(filename).toLowerCase();
        let mimetype = 'application/octet-stream';
        
        if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)) {
          mimetype = `image/${ext.substring(1)}`;
        } else if (['.mp4', '.avi', '.mov', '.wmv', '.webm'].includes(ext)) {
          mimetype = `video/${ext.substring(1)}`;
        } else if (ext === '.pdf') {
          mimetype = 'application/pdf';
        } else if (['.doc', '.docx'].includes(ext)) {
          mimetype = 'application/msword';
        } else if (['.xls', '.xlsx'].includes(ext)) {
          mimetype = 'application/vnd.ms-excel';
        } else if (ext === '.txt') {
          mimetype = 'text/plain';
        } else if (ext === '.csv') {
          mimetype = 'text/csv';
        }
        
        // Create file record
        const fileRecord = new File({
          filename,
          originalName,
          mimetype,
          size: stats.size,
          path: filePath,
          url: `${process.env.API_BASE_URL || 'http://localhost:8002'}/api/files/serve/${filename}`,
          uploadedBy: adminUser._id,
          isActive: true,
          description: `Auto-synced file from uploads directory`,
          tags: ['auto-sync'],
          createdAt: stats.birthtime,
          updatedAt: stats.mtime
        });
        
        try {
          await fileRecord.save();
          console.log(`✅ Synced: ${originalName} (${fileRecord.getFormattedSize()})`);
          totalSynced++;
        } catch (error) {
          console.log(`❌ Error syncing ${filename}:`, error.message);
        }
      }
    }
    
    console.log(`\n🎉 Sync completed! ${totalSynced} files synced to database.`);
    
    // Show updated statistics
    const stats = await File.getStats();
    console.log('\n📊 Updated File Statistics:');
    console.log('  Total Files:', stats.totalFiles);
    console.log('  Total Size:', formatBytes(stats.totalSize));
    console.log('  Recent Uploads:', stats.recentUploads);
    console.log('  Files by Type:');
    stats.filesByType.forEach(type => {
      console.log(`    ${type.type}: ${type.count} files (${formatBytes(type.size)})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

syncFilesToDatabase();
