const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Post = require('./api/models/post');
const User = require('./api/models/user');
const PostCat = require('./api/models/post_cat');

dotenv.config();
mongoose.connect(process.env.MONGO_URL, { useNewUrlParser: true });

async function createTestPost() {
  try {
    // Get first user and category
    const user = await User.findOne();
    const category = await PostCat.findOne();
    
    if (!user || !category) {
      console.log('Need user and category to create test post');
      mongoose.connection.close();
      return;
    }
    
    console.log('Found user:', user.username);
    console.log('Found category:', category.name);
    
    // Create multiple test posts
    const testPosts = [
      {
        title: 'Bài viết test chờ kích hoạt 1',
        desc: '<p><PERSON><PERSON><PERSON> là bài viết test 1 để kiểm tra tính năng chờ kích hoạt.</p>',
        short: '<PERSON>ài viết test chờ kích hoạt 1',
        slug: 'bai-viet-test-cho-kich-hoat-1',
      },
      {
        title: 'Bài viết test chờ kích hoạt 2',
        desc: '<p>Đây là bài viết test 2 để kiểm tra tính năng chờ kích hoạt.</p>',
        short: 'Bài viết test chờ kích hoạt 2',
        slug: 'bai-viet-test-cho-kich-hoat-2',
      },
      {
        title: 'Bài viết test chờ kích hoạt 3',
        desc: '<p>Đây là bài viết test 3 để kiểm tra tính năng chờ kích hoạt.</p>',
        short: 'Bài viết test chờ kích hoạt 3',
        slug: 'bai-viet-test-cho-kich-hoat-3',
      }
    ];

    for (let i = 0; i < testPosts.length; i++) {
      const postData = testPosts[i];
      const testPost = new Post({
        ...postData,
        user: user._id,
        categories: [category._id],
        isActive: false, // Đặt thành false để test
        isFeature: false,
        index: 0,
        featureImg: {
          _id: `test-img-id-${i}`,
          path: 'uploads/media/placeholder.jpg',
          folder: 'media'
        }
      });

      await testPost.save();
      console.log(`✅ Created test post ${i + 1} with isActive: false`);
      console.log('Post ID:', testPost._id);
      console.log('Title:', testPost.title);
      console.log('isActive:', testPost.isActive);
      console.log('---');
    }
    
    // Verify counts
    const totalPosts = await Post.countDocuments();
    const inactivePosts = await Post.countDocuments({ isActive: false });
    console.log('\n=== UPDATED STATISTICS ===');
    console.log('Total posts:', totalPosts);
    console.log('Inactive posts (isActive: false):', inactivePosts);
    
    mongoose.connection.close();
  } catch (error) {
    console.error('Error:', error);
    mongoose.connection.close();
  }
}

createTestPost();
