"use client";

import { useState } from "react";
import FileUploadModal from "@/components/FileManager/FileUploadModal";

const FileUploadDemo = () => {
  const [showModal, setShowModal] = useState(false);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">File Upload Demo</h1>
      <p className="text-gray-600 mb-6">
        This is a demo page to test the file upload modal functionality.
      </p>
      
      <button
        onClick={() => setShowModal(true)}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Open Upload Modal
      </button>

      {showModal && (
        <FileUploadModal
          onClose={() => setShowModal(false)}
          onUploadSuccess={() => {
            setShowModal(false);
            alert("Upload successful!");
          }}
        />
      )}
    </div>
  );
};

export default FileUploadDemo;
