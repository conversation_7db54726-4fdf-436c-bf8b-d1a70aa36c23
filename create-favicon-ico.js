const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function createFaviconIco() {
  try {
    // Create public directory if it doesn't exist
    const publicDir = path.join(__dirname, 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // Create a simple favicon SVG
    const svg = `
      <svg width="32" height="32" xmlns="http://www.w3.org/2000/svg">
        <rect width="32" height="32" fill="#3B82F6"/>
        <text x="16" y="22" font-family="Arial, sans-serif" font-size="20" fill="white" text-anchor="middle" font-weight="bold">
          T
        </text>
      </svg>
    `;

    // Create favicon.ico (browsers accept PNG with .ico extension)
    const faviconPath = path.join(publicDir, 'favicon.ico');
    await sharp(Buffer.from(svg))
      .resize(32, 32)
      .png()
      .toFile(faviconPath);

    console.log('Favicon.ico created at:', faviconPath);

    // Verify file exists
    if (fs.existsSync(faviconPath)) {
      const stats = fs.statSync(faviconPath);
      console.log('File size:', stats.size, 'bytes');
    }

  } catch (error) {
    console.error('Error creating favicon.ico:', error);
  }
}

createFaviconIco();
