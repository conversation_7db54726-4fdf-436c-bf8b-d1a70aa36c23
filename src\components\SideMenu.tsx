"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import DashboardLogo from "./Navigation/DashboardLogo";
import ButtonLogout from "./ui/button-logout";
import { useAuth } from "@/hooks/useAuth";
import { useState } from "react";
import {
  FileText,
  Users,
  Calendar,
  Settings,
  Folder,
  Menu as MenuIcon,
  ChevronDown,
  ChevronRight,
  Home,
  User,
  Shield,
  Upload
} from "react-feather";

export default function SideMenu() {
  const { hasPermission } = useAuth();
  const pathname = usePathname();
  const isSecretRoute = pathname.startsWith("/dashboard/secret");
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  const toggleMenu = (href: string) => {
    setExpandedMenus(prev =>
      prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  const isMenuExpanded = (href: string) => expandedMenus.includes(href);
  const isActiveRoute = (href: string) => pathname === href || pathname.startsWith(href + '/');

  const MenuIcon_Component = ({ icon: Icon, className = "" }: { icon: any, className?: string }) => (
    <Icon size={18} className={className} />
  );
  const secretMenu = [
    {
      title: "Tin Tức",
      href: "/dashboard/secret/blog",
      icon: FileText,
      children: [
        { title: "Tin Tức", href: "/dashboard/secret/blog" },
        { title: "Tin Nổi Bật", href: "/dashboard/secret/blog/feature" },
        { title: "Thêm Tin Tức", href: "/dashboard/secret/blog/add" },
      ],
    },
    {
      title: "Danh Mục",
      href: "/dashboard/secret/categories",
      icon: Folder,
      children: [
        { title: "Quản Lý Danh Mục", href: "/dashboard/secret/categories" },
        { title: "Thêm Danh Mục", href: "/dashboard/secret/categories/add" },
      ],
    },
    {
      title: "Lịch xét xử",
      href: "/dashboard/manager/schedule",
      icon: Calendar,
      children: [
        { title: "Thêm lịch xét xử", href: "/dashboard/manager/schedule/import" },
        { title: "Quản Lý lịch xét xử", href: "/dashboard/manager/schedule/manage" },
      ],
    },
    {
      title: "Thành Viên",
      href: "/dashboard/secret/user",
      icon: Users,
      children: [
        { title: "Quản Lý Thành Viên", href: "/dashboard/secret/user" },
        { title: "Thêm Thành Viên", href: "/dashboard/secret/user/add" },
        { title: "Nhập File CSV", href: "/dashboard/secret/user/import" },
      ],
    },
    {
      title: "Trang",
      href: "/dashboard/secret/page",
      icon: FileText,
      children: [
        { title: "Quản Lý Trang", href: "/dashboard/secret/page" },
        { title: "Thêm trang", href: "/dashboard/secret/page/add" },
      ],
    },
    {
      title: "Menu",
      href: "/dashboard/secret/menu",
      icon: MenuIcon,
      children: [
        { title: "Quản Lý Menu", href: "/dashboard/secret/menu" },
        { title: "Thêm menu", href: "/dashboard/secret/menu/add" },
      ],
    },
    {
      title: "Quản Lý File",
      href: "/dashboard/secret/files",
      icon: Upload,
      children: [
        { title: "Quản Lý File", href: "/dashboard/secret/files" },
      ],
    },
    {
      title: "Cài đặt",
      href: "/dashboard/secret/setting",
      icon: Settings,
    },
  ];
  const generalMenu = [
    { title: "Tổng quan", href: "/dashboard", icon: Home },
    { title: "Chỉnh sửa thông tin", href: "/dashboard/account", icon: User },
  ];

  const userMenu = [
    {
      title: "Tin Tức",
      href: "/dashboard/account/blog",
      icon: FileText,
      children: [
        { title: "Tin Tức", href: "/dashboard/account/blog" },
        { title: "Thêm Tin Tức", href: "/dashboard/account/blog/add" },
      ],
    },
  ];

  const ManagerMenu = [
    {
      title: "Cài đặt",
      href: "/dashboard/manager/setting",
      icon: Settings,
    },
    {
      title: "Lịch xét xử",
      href: "/dashboard/manager/schedule",
      icon: Calendar,
      children: [
        { title: "Thêm lịch xét xử", href: "/dashboard/manager/schedule/import" },
        { title: "Quản Lý lịch xét xử", href: "/dashboard/manager/schedule/manage" },
      ],
    },
    {
      title: "Quản lý tin tức",
      href: "/dashboard/manager/blog",
      icon: FileText,
    },
    {
      title: "Quản Lý File",
      href: "/dashboard/secret/files",
      icon: Upload,
    },
  ];
  const MenuItem = ({ item, isChild = false }: { item: any, isChild?: boolean }) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = isActiveRoute(item.href);
    const isExpanded = isMenuExpanded(item.href);

    return (
      <li className={`${isChild ? 'ml-4' : ''}`}>
        <div className="flex items-center">
          {hasChildren ? (
            <button
              onClick={() => toggleMenu(item.href)}
              className={`flex items-center w-full px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              {item.icon && (
                <MenuIcon_Component
                  icon={item.icon}
                  className={`mr-3 ${isActive ? 'text-white' : 'text-gray-500'}`}
                />
              )}
              <span className="flex-1 font-medium">{item.title}</span>
              {hasChildren && (
                <MenuIcon_Component
                  icon={isExpanded ? ChevronDown : ChevronRight}
                  className={`ml-2 ${isActive ? 'text-white' : 'text-gray-400'}`}
                />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              className={`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              {item.icon && (
                <MenuIcon_Component
                  icon={item.icon}
                  className={`mr-3 ${isActive ? 'text-white' : 'text-gray-500'}`}
                />
              )}
              <span className="font-medium">{item.title}</span>
            </Link>
          )}
        </div>

        {hasChildren && isExpanded && (
          <ul className="mt-2 space-y-1 ml-4">
            {item.children.map((child: any) => (
              <li key={child.href}>
                <Link
                  href={child.href}
                  className={`flex items-center px-4 py-2 text-sm rounded-lg transition-all duration-200 ${
                    pathname === child.href
                      ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <span className="w-2 h-2 bg-gray-300 rounded-full mr-3"></span>
                  {child.title}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </li>
    );
  };

  return (
    <nav className="md:left-0 md:block md:fixed md:top-0 md:bottom-0 md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden flex flex-wrap items-center justify-between md:w-64 z-10 py-0 md:py-6 px-0 bg-white shadow-xl border-r border-gray-200">
      <div className="w-full">
        {/* Logo Section */}
        <div className="px-6 py-4 border-b border-gray-200">
          <DashboardLogo />
        </div>

        {/* Menu Section */}
        <div className="px-4 py-6">
          {isSecretRoute ? (
            <div className="space-y-2">
              {secretMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              <div className="pt-4 mt-6 border-t border-gray-200">
                <MenuItem
                  item={{
                    title: "Truy Cập Tài Khoản",
                    href: "/dashboard/",
                    icon: User
                  }}
                />
                <div className="mt-4">
                  <ButtonLogout />
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {generalMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("user") && userMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("manager") && ManagerMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("admin") && (
                <div className="pt-4 mt-6 border-t border-gray-200">
                  <MenuItem
                    item={{
                      title: "Truy Cập Quản Lý",
                      href: "/dashboard/secret",
                      icon: Shield
                    }}
                  />
                </div>
              )}

              <div className="pt-4 mt-6 border-t border-gray-200">
                <ButtonLogout />
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}
