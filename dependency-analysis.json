{"totalDependencies": 35, "usedDependencies": ["@casl/ability", "@casl/mongoose", "axios", "bcryptjs", "cors", "dotenv", "express", "express-mongo-sanitize", "express-rate-limit", "helmet", "hpp", "jsonwebtoken", "moment", "mongoose", "morgan", "multer", "nanoid", "nodemailer", "passport", "passport-jwt", "qrcode", "request-ip", "sharp", "speakeasy", "validator", "xss"], "unusedDependencies": ["body-parser", "eslint", "exceljs", "express-validator", "moment-timezone", "nodemon", "telegraf", "vietqr", "vnpay"], "usageRate": "74%"}