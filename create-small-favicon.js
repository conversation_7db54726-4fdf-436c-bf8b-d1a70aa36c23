const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function createSmallFavicon() {
  try {
    // Create public directory if it doesn't exist
    const publicDir = path.join(__dirname, 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // Create a simple favicon SVG
    const svg = `
      <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg">
        <rect width="16" height="16" fill="#3B82F6"/>
        <text x="8" y="12" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle" font-weight="bold">
          T
        </text>
      </svg>
    `;

    // Create small favicon.ico (16x16)
    const faviconPath = path.join(publicDir, 'favicon.ico');
    await sharp(Buffer.from(svg))
      .resize(16, 16)
      .png()
      .toFile(faviconPath);

    console.log('Small favicon.ico created at:', faviconPath);

    // Create 32x32 version
    const favicon32Path = path.join(publicDir, 'favicon-32x32.png');
    await sharp(Buffer.from(svg.replace('width="16"', 'width="32"').replace('height="16"', 'height="32"').replace('font-size="10"', 'font-size="20"').replace('y="12"', 'y="22"').replace('x="8"', 'x="16"')))
      .resize(32, 32)
      .png()
      .toFile(favicon32Path);

    console.log('32x32 favicon created at:', favicon32Path);

    // Verify file sizes
    if (fs.existsSync(faviconPath)) {
      const stats = fs.statSync(faviconPath);
      console.log('Favicon.ico size:', stats.size, 'bytes');
    }

  } catch (error) {
    console.error('Error creating small favicon:', error);
  }
}

createSmallFavicon();
